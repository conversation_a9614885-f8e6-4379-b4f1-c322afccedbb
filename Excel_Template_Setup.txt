INVOICE MANAGEMENT SYSTEM - EXCEL TEMPLATE SETUP

=== STEP 1: CREATE NEW EXCEL WORKBOOK ===
1. Open Excel
2. Create a new blank workbook
3. Save as "Invoice_Management_System.xlsm" (Excel Macro-Enabled Workbook)

=== STEP 2: CREATE WORKSHEETS ===
Create 4 worksheets with these exact names:
1. Invoice Data
2. Accounts  
3. Bank Statements
4. Settings

=== STEP 3: INVOICE DATA SHEET SETUP ===
In the "Invoice Data" sheet, create headers in row 1:

A1: Item Number
B1: Supplier Name
C1: Delivery Note Number
D1: Invoice Number
E1: Quote Number
F1: Account
G1: Date
H1: Category
I1: Description
J1: Amount Excl VAT
K1: Amount Incl VAT
L1: VAT Amount
M1: VAT Category
N1: Document Folder Path

Format the header row:
- Bold text
- Blue background (RGB: 68, 114, 196)
- White text color
- Center alignment

=== STEP 4: ACCOUNTS SHEET SETUP ===
In the "Accounts" sheet:

Row 1:
A1: Supplier Filter:
C1: Date From:
E1: Date To:

Row 2:
B2: [Leave empty for dropdown]
D2: [Leave empty for date input]
F2: [Leave empty for date input]
G2: Refresh Data [This will be a button]

Row 4 (Headers):
A4: Supplier Name
B4: Document Type
C4: Document Number
D4: Date
E4: Description
F4: Amount Excl
G4: Amount Incl
H4: Balance Impact
I4: Open Folder

Format row 4 same as Invoice Data headers.

=== STEP 5: BANK STATEMENTS SHEET SETUP ===
In the "Bank Statements" sheet, create headers in row 1:

A1: Date
B1: Description
C1: Reference
D1: Amount
E1: Supplier
F1: Account
G1: Category

Format same as other headers.

=== STEP 6: SETTINGS SHEET SETUP ===
In the "Settings" sheet:

Column A (Accounts):
A1: Accounts:
A2: Tumuga
A3: Choice Grade

Column C (Categories):
C1: Categories:
C2: Repairs and Maintenance
C3: Office Supplies
C4: Fuel
C5: Insurance
C6: Professional Services
C7: Equipment
C8: Utilities

Column E (VAT Categories):
E1: VAT Categories:
E2: Standard Rate (15%)
E3: Zero Rate (0%)
E4: Exempt
E5: Input VAT
E6: Output VAT

=== STEP 7: DATA VALIDATION SETUP ===
Set up dropdowns:

1. Invoice Data Sheet - Column F (Account):
   - Select column F
   - Data > Data Validation
   - Allow: List
   - Source: Tumuga,Choice Grade

2. Invoice Data Sheet - Column H (Category):
   - Select column H
   - Data > Data Validation
   - Allow: List
   - Source: Repairs and Maintenance,Office Supplies,Fuel,Insurance,Professional Services,Equipment,Utilities

3. Invoice Data Sheet - Column M (VAT Category):
   - Select column M
   - Data > Data Validation
   - Allow: List
   - Source: Standard Rate (15%),Zero Rate (0%),Exempt,Input VAT,Output VAT

4. Invoice Data Sheet - Column G (Date):
   - Select column G
   - Data > Data Validation
   - Allow: Date
   - Between: 1/1/2020 and 31/12/2030

=== STEP 8: FORMATTING ===
1. Select columns J, K, L in Invoice Data sheet
   - Format as Currency (R #,##0.00)

2. Select column G in Invoice Data sheet
   - Format as Date (dd/mm/yyyy)

3. Auto-fit all columns in all sheets

=== STEP 9: IMPORT VBA CODE ===
1. Press Alt+F11 to open VBA Editor
2. Import each .bas file:
   - Right-click in Project Explorer
   - Import File
   - Select the .bas file

3. For worksheet events:
   - Double-click "Sheet1 (Invoice Data)" in Project Explorer
   - Copy the Invoice Data worksheet events from VBA_WorksheetEvents.bas
   
   - Double-click "Sheet2 (Accounts)" in Project Explorer  
   - Copy the Accounts worksheet events from VBA_WorksheetEvents.bas
   
   - Double-click "ThisWorkbook" in Project Explorer
   - Copy the workbook events from VBA_WorksheetEvents.bas

=== STEP 10: INITIALIZE SYSTEM ===
1. Close VBA Editor (Alt+Q)
2. Press Alt+F8 to open Macros
3. Run "InitializeWorkbook" macro
4. This will create buttons and finalize setup

=== STEP 11: TEST THE SYSTEM ===
1. Go to Invoice Data sheet
2. Click "Add New Row" button
3. Enter test data:
   - Supplier: Test Supplier
   - Invoice Number: INV001
   - Account: Tumuga
   - Date: Today's date
   - Category: Office Supplies
   - Description: Test item
   - Amount Excl VAT: 100
   - VAT Category: Standard Rate (15%)

4. VAT should auto-calculate to 15.00
5. Amount Incl VAT should show 115.00
6. Item Number should show 1

=== STEP 12: SET UP POWER QUERIES (OPTIONAL) ===
1. Go to Data > Get Data > From Other Sources > Blank Query
2. In Advanced Editor, paste the M code from PowerQuery_InvoiceTransform.m
3. Create separate queries for each transformation
4. Load to new worksheets or existing sheets as needed

=== USAGE TIPS ===
1. Always enter supplier names consistently
2. Use the document folder path to link to physical/digital document storage
3. The system will auto-calculate item numbers for multiple items per invoice
4. Use the Accounts sheet to filter and view data by supplier and date
5. Bank Statements sheet helps track payments and calculate balances
6. Double-click "Open Folder" cells in Accounts sheet to open document folders

=== TROUBLESHOOTING ===
- If macros don't work: Check Excel macro security settings
- If dropdowns are empty: Re-run data validation setup
- If calculations don't work: Check that VBA code is properly imported
- If buttons don't appear: Run InitializeWorkbook macro again

This template provides a complete invoice management system with:
- Automated VAT calculations
- Item numbering
- Supplier filtering
- Document management
- Balance tracking
- Reporting capabilities
