PK                  ! xl/workbook.xml<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
  <fileVersion appName="xl" lastEdited="7" lowestEdited="7" rupBuild="24816"/>
  <workbookPr defaultThemeVersion="166925"/>
  <bookViews>
    <workbookView xWindow="0" yWindow="0" windowWidth="28800" windowHeight="17460"/>
  </bookViews>
  <sheets>
    <sheet name="Invoice Data" sheetId="1" r:id="rId1"/>
    <sheet name="Accounts" sheetId="2" r:id="rId2"/>
    <sheet name="Bank Statements" sheetId="3" r:id="rId3"/>
    <sheet name="Settings" sheetId="4" r:id="rId4"/>
  </sheets>
  <calcPr calcId="191029"/>
</workbook>PK                  ! xl/worksheets/sheet1.xml<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
  <sheetData>
    <row r="1">
      <c r="A1" t="str"><v>Item Number</v></c>
      <c r="B1" t="str"><v>Supplier Name</v></c>
      <c r="C1" t="str"><v>Delivery Note Number</v></c>
      <c r="D1" t="str"><v>Invoice Number</v></c>
      <c r="E1" t="str"><v>Quote Number</v></c>
      <c r="F1" t="str"><v>Account</v></c>
      <c r="G1" t="str"><v>Date</v></c>
      <c r="H1" t="str"><v>Category</v></c>
      <c r="I1" t="str"><v>Description</v></c>
      <c r="J1" t="str"><v>Amount Excl VAT</v></c>
      <c r="K1" t="str"><v>Amount Incl VAT</v></c>
      <c r="L1" t="str"><v>VAT Amount</v></c>
      <c r="M1" t="str"><v>VAT Category</v></c>
      <c r="N1" t="str"><v>Document Folder Path</v></c>
    </row>
  </sheetData>
</worksheet>PK
