Attribute VB_Name = "AccountsModule"
Option Explicit

' Refresh the accounts data based on filters
Sub RefreshAccountsData()
    Dim wsAccounts As Worksheet
    Dim wsInvoice As Worksheet
    Dim wsBank As Worksheet
    Dim supplierFilter As String
    Dim dateFrom As Date
    Dim dateTo As Date
    Dim lastRow As Long
    Dim outputRow As Long
    Dim i As Long
    
    Set wsAccounts = ThisWorkbook.Worksheets(ACCOUNTS_SHEET)
    Set wsInvoice = ThisWorkbook.Worksheets(INVOICE_SHEET)
    Set wsBank = ThisWorkbook.Worksheets(BANK_SHEET)
    
    ' Get filter values
    supplierFilter = wsAccounts.Cells(2, 2).Value
    
    On Error Resume Next
    dateFrom = CDate(wsAccounts.Cells(2, 4).Value)
    dateTo = CDate(wsAccounts.Cells(2, 6).Value)
    On Error GoTo 0
    
    If dateFrom = 0 Then dateFrom = DateSerial(1900, 1, 1)
    If dateTo = 0 Then dateTo = DateSerial(2100, 12, 31)
    
    ' Clear existing data
    wsAccounts.Range("A5:I1000").Clear
    
    outputRow = 5
    
    ' Process invoice data
    lastRow = wsInvoice.Cells(wsInvoice.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    
    For i = 2 To lastRow
        Dim supplierName As String
        Dim docDate As Date
        Dim docType As String
        Dim docNumber As String
        Dim description As String
        Dim amountExcl As Double
        Dim amountIncl As Double
        Dim balanceImpact As String
        Dim folderPath As String
        
        supplierName = wsInvoice.Cells(i, COL_SUPPLIER_NAME).Value
        
        On Error Resume Next
        docDate = CDate(wsInvoice.Cells(i, COL_DATE).Value)
        On Error GoTo 0
        
        ' Check filters
        If (supplierFilter = "" Or InStr(1, supplierName, supplierFilter, vbTextCompare) > 0) And _
           (docDate >= dateFrom And docDate <= dateTo) Then
            
            ' Determine document type and number
            If wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value <> "" Then
                docType = "Invoice"
                docNumber = wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value
                balanceImpact = "Debit"
            ElseIf wsInvoice.Cells(i, COL_QUOTE_NUMBER).Value <> "" Then
                docType = "Quote"
                docNumber = wsInvoice.Cells(i, COL_QUOTE_NUMBER).Value
                balanceImpact = "No Impact"
            ElseIf wsInvoice.Cells(i, COL_DELIVERY_NOTE).Value <> "" Then
                docType = "Delivery Note"
                docNumber = wsInvoice.Cells(i, COL_DELIVERY_NOTE).Value
                balanceImpact = "No Impact"
            Else
                docType = "Unknown"
                docNumber = ""
                balanceImpact = "No Impact"
            End If
            
            description = wsInvoice.Cells(i, COL_DESCRIPTION).Value
            amountExcl = wsInvoice.Cells(i, COL_AMOUNT_EXL).Value
            amountIncl = wsInvoice.Cells(i, COL_AMOUNT_INCL).Value
            folderPath = wsInvoice.Cells(i, COL_DOCUMENT_PATH).Value
            
            ' Output to accounts sheet
            wsAccounts.Cells(outputRow, 1).Value = supplierName
            wsAccounts.Cells(outputRow, 2).Value = docType
            wsAccounts.Cells(outputRow, 3).Value = docNumber
            wsAccounts.Cells(outputRow, 4).Value = docDate
            wsAccounts.Cells(outputRow, 5).Value = description
            wsAccounts.Cells(outputRow, 6).Value = amountExcl
            wsAccounts.Cells(outputRow, 7).Value = amountIncl
            wsAccounts.Cells(outputRow, 8).Value = balanceImpact
            
            ' Add button for opening folder
            If folderPath <> "" Then
                wsAccounts.Cells(outputRow, 9).Value = "Open Folder"
                wsAccounts.Cells(outputRow, 9).Interior.Color = RGB(144, 238, 144)
                wsAccounts.Cells(outputRow, 9).Font.Bold = True
                wsAccounts.Cells(outputRow, 9).HorizontalAlignment = xlCenter
            End If
            
            outputRow = outputRow + 1
        End If
    Next i
    
    ' Process bank statement data (payments)
    lastRow = wsBank.Cells(wsBank.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        supplierName = wsBank.Cells(i, 5).Value ' Supplier column
        
        On Error Resume Next
        docDate = CDate(wsBank.Cells(i, 1).Value) ' Date column
        On Error GoTo 0
        
        ' Check filters
        If (supplierFilter = "" Or InStr(1, supplierName, supplierFilter, vbTextCompare) > 0) And _
           (docDate >= dateFrom And docDate <= dateTo) And _
           supplierName <> "" Then
            
            description = wsBank.Cells(i, 2).Value ' Description
            Dim paymentAmount As Double
            paymentAmount = wsBank.Cells(i, 4).Value ' Amount
            
            ' Output payment to accounts sheet
            wsAccounts.Cells(outputRow, 1).Value = supplierName
            wsAccounts.Cells(outputRow, 2).Value = "Payment"
            wsAccounts.Cells(outputRow, 3).Value = wsBank.Cells(i, 3).Value ' Reference
            wsAccounts.Cells(outputRow, 4).Value = docDate
            wsAccounts.Cells(outputRow, 5).Value = description
            wsAccounts.Cells(outputRow, 6).Value = paymentAmount
            wsAccounts.Cells(outputRow, 7).Value = paymentAmount
            wsAccounts.Cells(outputRow, 8).Value = "Credit"
            
            outputRow = outputRow + 1
        End If
    Next i
    
    ' Format the data
    If outputRow > 5 Then
        With wsAccounts.Range("A5:I" & (outputRow - 1))
            .Borders.LineStyle = xlContinuous
            .Borders.Weight = xlThin
        End With
        
        ' Format amounts as currency
        wsAccounts.Range("F5:G" & (outputRow - 1)).NumberFormat = "R #,##0.00"
    End If
    
    wsAccounts.Columns.AutoFit
    
    MsgBox "Accounts data refreshed successfully!", vbInformation
End Sub

' Open folder containing documents
Sub OpenDocumentFolder(folderPath As String)
    If folderPath = "" Then
        MsgBox "No folder path specified for this item.", vbExclamation
        Exit Sub
    End If
    
    If Dir(folderPath, vbDirectory) <> "" Then
        Shell "explorer.exe " & Chr(34) & folderPath & Chr(34), vbNormalFocus
    Else
        MsgBox "Folder path does not exist: " & folderPath, vbExclamation
    End If
End Sub

' Calculate supplier balance
Function CalculateSupplierBalance(supplierName As String, accountType As String) As Double
    Dim wsInvoice As Worksheet
    Dim wsBank As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim balance As Double
    
    Set wsInvoice = ThisWorkbook.Worksheets(INVOICE_SHEET)
    Set wsBank = ThisWorkbook.Worksheets(BANK_SHEET)
    
    balance = 0
    
    ' Add invoice amounts (debits)
    lastRow = wsInvoice.Cells(wsInvoice.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    
    For i = 2 To lastRow
        If wsInvoice.Cells(i, COL_SUPPLIER_NAME).Value = supplierName And _
           wsInvoice.Cells(i, COL_ACCOUNT).Value = accountType And _
           wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value <> "" Then
            
            balance = balance + wsInvoice.Cells(i, COL_AMOUNT_INCL).Value
        End If
    Next i
    
    ' Subtract payment amounts (credits)
    lastRow = wsBank.Cells(wsBank.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If wsBank.Cells(i, 5).Value = supplierName And _
           wsBank.Cells(i, 6).Value = accountType Then
            
            balance = balance - wsBank.Cells(i, 4).Value
        End If
    Next i
    
    CalculateSupplierBalance = balance
End Function

' Create summary report
Sub CreateSupplierSummary()
    Dim wsAccounts As Worksheet
    Dim wsInvoice As Worksheet
    Dim suppliers As Collection
    Dim supplier As Variant
    Dim i As Long
    Dim lastRow As Long
    Dim outputRow As Long
    
    Set wsAccounts = ThisWorkbook.Worksheets(ACCOUNTS_SHEET)
    Set wsInvoice = ThisWorkbook.Worksheets(INVOICE_SHEET)
    Set suppliers = New Collection
    
    ' Get unique suppliers
    lastRow = wsInvoice.Cells(wsInvoice.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    
    For i = 2 To lastRow
        Dim supplierName As String
        supplierName = wsInvoice.Cells(i, COL_SUPPLIER_NAME).Value
        
        If supplierName <> "" Then
            On Error Resume Next
            suppliers.Add supplierName, supplierName
            On Error GoTo 0
        End If
    Next i
    
    ' Clear summary area (starting from column K)
    wsAccounts.Range("K:O").Clear
    
    ' Headers for summary
    wsAccounts.Cells(1, 11) = "Supplier Summary"
    wsAccounts.Cells(3, 11) = "Supplier"
    wsAccounts.Cells(3, 12) = "Tumuga Balance"
    wsAccounts.Cells(3, 13) = "Choice Grade Balance"
    wsAccounts.Cells(3, 14) = "Total Balance"
    
    ' Format summary headers
    With wsAccounts.Range("K3:N3")
        .Font.Bold = True
        .Interior.Color = RGB(68, 114, 196)
        .Font.Color = RGB(255, 255, 255)
        .HorizontalAlignment = xlCenter
    End With
    
    outputRow = 4
    
    ' Calculate balances for each supplier
    For Each supplier In suppliers
        Dim tumugaBalance As Double
        Dim choiceGradeBalance As Double
        Dim totalBalance As Double
        
        tumugaBalance = CalculateSupplierBalance(supplier, "Tumuga")
        choiceGradeBalance = CalculateSupplierBalance(supplier, "Choice Grade")
        totalBalance = tumugaBalance + choiceGradeBalance
        
        wsAccounts.Cells(outputRow, 11).Value = supplier
        wsAccounts.Cells(outputRow, 12).Value = tumugaBalance
        wsAccounts.Cells(outputRow, 13).Value = choiceGradeBalance
        wsAccounts.Cells(outputRow, 14).Value = totalBalance
        
        outputRow = outputRow + 1
    Next supplier
    
    ' Format summary data
    If outputRow > 4 Then
        With wsAccounts.Range("K4:N" & (outputRow - 1))
            .Borders.LineStyle = xlContinuous
            .Borders.Weight = xlThin
        End With
        
        ' Format amounts as currency
        wsAccounts.Range("L4:N" & (outputRow - 1)).NumberFormat = "R #,##0.00"
    End If
    
    wsAccounts.Columns("K:N").AutoFit
End Sub
