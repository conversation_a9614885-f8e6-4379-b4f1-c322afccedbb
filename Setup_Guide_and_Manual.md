# Invoice Management System - Setup Guide and User Manual

## Overview
This Excel-based Invoice Management System helps you track invoices, quotes, and delivery notes from different suppliers across multiple accounts (Tumuga and Choice Grade). It includes automated calculations, filtering capabilities, and document management features.

## Files Included
1. `Invoice_Management_System.xlsm` - Main Excel workbook
2. `VBA_MainModule.bas` - Core VBA functions
3. `VBA_AccountsModule.bas` - Accounts sheet functionality
4. `VBA_EventHandlers.bas` - Event handling and validation
5. `VBA_WorksheetEvents.bas` - Worksheet-specific events
6. `PowerQuery_InvoiceTransform.m` - Power Query transformations

## Setup Instructions

### Step 1: Enable Macros in Excel
1. Open Excel
2. Go to File > Options > Trust Center > Trust Center Settings
3. Click on "Macro Settings"
4. Select "Enable all macros" (or "Disable all macros with notification")
5. Check "Trust access to the VBA project object model"
6. Click OK

### Step 2: Import VBA Code
1. Open the `Invoice_Management_System.xlsm` file
2. Press Alt+F11 to open the VBA Editor
3. For each .bas file:
   - Right-click in the Project Explorer
   - Select "Import File"
   - Choose the .bas file
   - Click "Open"

### Step 3: Set Up Worksheet Events
1. In VBA Editor, double-click on "Sheet1 (Invoice Data)" in Project Explorer
2. Copy the worksheet change events from `VBA_WorksheetEvents.bas`
3. Repeat for "Sheet2 (Accounts)" and "ThisWorkbook"

### Step 4: Initialize the System
1. Close VBA Editor (Alt+Q)
2. In Excel, press Alt+F8 to open Macros dialog
3. Run "InitializeWorkbook" macro
4. This will set up all sheets, headers, and formatting

### Step 5: Set Up Power Queries (Optional)
1. Go to Data > Get Data > From Other Sources > Blank Query
2. In Power Query Editor, go to Advanced Editor
3. Copy and paste the M code from `PowerQuery_InvoiceTransform.m`
4. Create separate queries for each transformation

## User Manual

### Invoice Data Sheet

#### Column Descriptions:
- **Item Number**: Auto-calculated sequence for items within the same document
- **Supplier Name**: Name of the supplier/vendor
- **Delivery Note Number**: Reference number for delivery notes
- **Invoice Number**: Invoice reference number
- **Quote Number**: Quote reference number
- **Account**: Select between "Tumuga" or "Choice Grade"
- **Date**: Transaction date
- **Category**: Expense category (Repairs, Office Supplies, etc.)
- **Description**: Item description
- **Amount Excl VAT**: Amount excluding VAT
- **Amount Incl VAT**: Amount including VAT
- **VAT Amount**: VAT amount (auto-calculated)
- **VAT Category**: Type of VAT (Standard 15%, Zero Rate, etc.)
- **Document Folder Path**: Path to folder containing related documents

#### Key Features:
1. **Auto-calculation**: VAT amounts are calculated automatically
2. **Item numbering**: Items are numbered sequentially per document
3. **Data validation**: Dropdowns for accounts, categories, and VAT types
4. **Document linking**: Store folder paths for easy access to documents

#### How to Add New Invoices:
1. Click "Add New Row" button or go to the next empty row
2. Enter supplier name
3. Enter document numbers (invoice, quote, or delivery note)
4. Select account (Tumuga or Choice Grade)
5. Enter date
6. Select category from dropdown
7. Enter description
8. Enter either amount excluding VAT or including VAT
9. Select VAT category - VAT will be calculated automatically
10. Browse for document folder path

### Accounts Sheet

#### Purpose:
View filtered invoice data by supplier and date range, including payment tracking.

#### How to Use Filters:
1. **Supplier Filter**: Select from dropdown or type supplier name
2. **Date From**: Enter start date (dd/mm/yyyy)
3. **Date To**: Enter end date (dd/mm/yyyy)
4. Click "Refresh Data" button

#### Features:
- **Balance Impact**: Shows whether transaction affects account balance
- **Open Folder**: Double-click to open document folder
- **Payment Tracking**: Integrates with bank statement data
- **Supplier Summary**: Click button to generate balance summary

### Bank Statements Sheet

#### Purpose:
Track payments made to suppliers to calculate outstanding balances.

#### Columns:
- **Date**: Payment date
- **Description**: Payment description
- **Reference**: Bank reference number
- **Amount**: Payment amount
- **Supplier**: Supplier name (must match Invoice Data sheet)
- **Account**: Account used (Tumuga or Choice Grade)
- **Category**: Payment category

### Automated Features

#### 1. Item Number Calculation
- Automatically assigns sequential numbers to items within the same document
- Resets to 1 for each new document number
- Updates when document numbers change

#### 2. VAT Calculations
- **Standard Rate (15%)**: Calculates 15% VAT
- **Zero Rate (0%)**: No VAT applied
- **Exempt**: No VAT applied
- Works both ways: enter excl amount to get incl amount, or vice versa

#### 3. Supplier Dropdown Updates
- Automatically updates supplier filter dropdown when new suppliers are added
- Maintains unique list of suppliers

#### 4. Balance Calculations
- Invoices increase supplier balance (debit)
- Payments decrease supplier balance (credit)
- Quotes and delivery notes don't affect balance

### Power Query Features (If Implemented)

#### 1. Invoice Data Transformation
- Cleans and validates data
- Adds calculated columns
- Performs VAT validation checks

#### 2. Supplier Summary
- Groups data by supplier and account
- Calculates totals and averages
- Shows last invoice date and aging

#### 3. Monthly Summary
- Breaks down expenses by month and category
- Useful for budgeting and trend analysis

#### 4. VAT Report
- Summarizes VAT by category and period
- Calculates effective VAT rates
- Helps with VAT return preparation

#### 5. Outstanding Invoices
- Identifies aging invoices
- Categorizes by age (0-30, 31-60, 61-90, 90+ days)
- Helps with follow-up activities

### Tips and Best Practices

#### 1. Data Entry:
- Always enter supplier names consistently
- Use the same format for document numbers
- Enter dates in dd/mm/yyyy format
- Use the dropdowns for standardized entries

#### 2. Document Management:
- Create a consistent folder structure for documents
- Store all related documents (invoice, quote, delivery note) in the same folder
- Use descriptive folder names

#### 3. Regular Maintenance:
- Update bank statement data regularly
- Run "Refresh Data" on Accounts sheet after adding new invoices
- Use "Supplier Summary" to review outstanding balances

#### 4. Backup:
- Save the file regularly (auto-save is enabled every 10 minutes)
- Keep backup copies of the file
- Export data to CSV periodically for additional backup

### Troubleshooting

#### Common Issues:
1. **Macros not working**: Ensure macros are enabled in Excel settings
2. **VAT not calculating**: Check that VAT category is selected
3. **Supplier dropdown empty**: Run "Update Suppliers" button
4. **Folder won't open**: Check that the folder path exists and is accessible

#### Error Messages:
- If you get VBA errors, check that all modules are properly imported
- Ensure worksheet names match exactly (case-sensitive)
- Verify that required columns exist in correct positions

### Support and Customization

#### Adding New Categories:
1. Go to Settings sheet
2. Add new categories to the list in column C
3. Update the validation formula in VBA code if needed

#### Adding New VAT Rates:
1. Go to Settings sheet
2. Add new VAT categories to column E
3. Update the VAT calculation logic in VBA code

#### Customizing Reports:
- Modify Power Query M code to add new calculated fields
- Create additional summary sheets as needed
- Add new buttons for custom functions

This system provides a comprehensive solution for invoice management with room for customization based on specific business needs.
