Attribute VB_Name = "MainModule"
Option Explicit

' Global variables for the invoice management system
Public Const INVOICE_SHEET = "Invoice Data"
Public Const ACCOUNTS_SHEET = "Accounts"
Public Const BANK_SHEET = "Bank Statements"
Public Const SETTINGS_SHEET = "Settings"

' Column constants for Invoice Data sheet
Public Const COL_ITEM_NUMBER = 1
Public Const COL_SUPPLIER_NAME = 2
Public Const COL_DELIVERY_NOTE = 3
Public Const COL_INVOICE_NUMBER = 4
Public Const COL_QUOTE_NUMBER = 5
Public Const COL_ACCOUNT = 6
Public Const COL_DATE = 7
Public Const COL_CATEGORY = 8
Public Const COL_DESCRIPTION = 9
Public Const COL_AMOUNT_EXL = 10
Public Const COL_AMOUNT_INCL = 11
Public Const COL_VAT_AMOUNT = 12
Public Const COL_VAT_CATEGORY = 13
Public Const COL_DOCUMENT_PATH = 14

' Initialize the workbook structure
Sub InitializeWorkbook()
    Call SetupInvoiceDataSheet
    Call SetupAccountsSheet
    Call SetupBankStatementsSheet
    Call SetupSettingsSheet
    Call CreateDataValidation
    MsgBox "Invoice Management System initialized successfully!", vbInformation
End Sub

' Setup the main Invoice Data sheet
Sub SetupInvoiceDataSheet()
    Dim ws As Worksheet
    Set ws = GetOrCreateSheet(INVOICE_SHEET)
    
    With ws
        .Cells.Clear
        
        ' Headers
        .Cells(1, COL_ITEM_NUMBER) = "Item Number"
        .Cells(1, COL_SUPPLIER_NAME) = "Supplier Name"
        .Cells(1, COL_DELIVERY_NOTE) = "Delivery Note Number"
        .Cells(1, COL_INVOICE_NUMBER) = "Invoice Number"
        .Cells(1, COL_QUOTE_NUMBER) = "Quote Number"
        .Cells(1, COL_ACCOUNT) = "Account"
        .Cells(1, COL_DATE) = "Date"
        .Cells(1, COL_CATEGORY) = "Category"
        .Cells(1, COL_DESCRIPTION) = "Description"
        .Cells(1, COL_AMOUNT_EXL) = "Amount Excl VAT"
        .Cells(1, COL_AMOUNT_INCL) = "Amount Incl VAT"
        .Cells(1, COL_VAT_AMOUNT) = "VAT Amount"
        .Cells(1, COL_VAT_CATEGORY) = "VAT Category"
        .Cells(1, COL_DOCUMENT_PATH) = "Document Folder Path"
        
        ' Format headers
        With .Range(.Cells(1, 1), .Cells(1, COL_DOCUMENT_PATH))
            .Font.Bold = True
            .Interior.Color = RGB(68, 114, 196)
            .Font.Color = RGB(255, 255, 255)
            .HorizontalAlignment = xlCenter
        End With
        
        ' Auto-fit columns
        .Columns.AutoFit
        
        ' Freeze top row
        .Rows(2).Select
        ActiveWindow.FreezePanes = True
    End With
End Sub

' Setup the Accounts sheet with filters
Sub SetupAccountsSheet()
    Dim ws As Worksheet
    Set ws = GetOrCreateSheet(ACCOUNTS_SHEET)
    
    With ws
        .Cells.Clear
        
        ' Filter controls
        .Cells(1, 1) = "Supplier Filter:"
        .Cells(1, 3) = "Date From:"
        .Cells(1, 5) = "Date To:"
        .Cells(2, 7) = "Refresh Data"
        
        ' Headers for filtered data (starting from row 4)
        .Cells(4, 1) = "Supplier Name"
        .Cells(4, 2) = "Document Type"
        .Cells(4, 3) = "Document Number"
        .Cells(4, 4) = "Date"
        .Cells(4, 5) = "Description"
        .Cells(4, 6) = "Amount Excl"
        .Cells(4, 7) = "Amount Incl"
        .Cells(4, 8) = "Balance Impact"
        .Cells(4, 9) = "Open Folder"
        
        ' Format headers
        With .Range(.Cells(4, 1), .Cells(4, 9))
            .Font.Bold = True
            .Interior.Color = RGB(68, 114, 196)
            .Font.Color = RGB(255, 255, 255)
            .HorizontalAlignment = xlCenter
        End With
        
        .Columns.AutoFit
    End With
End Sub

' Setup Bank Statements sheet
Sub SetupBankStatementsSheet()
    Dim ws As Worksheet
    Set ws = GetOrCreateSheet(BANK_SHEET)
    
    With ws
        .Cells.Clear
        
        ' Headers
        .Cells(1, 1) = "Date"
        .Cells(1, 2) = "Description"
        .Cells(1, 3) = "Reference"
        .Cells(1, 4) = "Amount"
        .Cells(1, 5) = "Supplier"
        .Cells(1, 6) = "Account"
        .Cells(1, 7) = "Category"
        
        ' Format headers
        With .Range(.Cells(1, 1), .Cells(1, 7))
            .Font.Bold = True
            .Interior.Color = RGB(68, 114, 196)
            .Font.Color = RGB(255, 255, 255)
            .HorizontalAlignment = xlCenter
        End With
        
        .Columns.AutoFit
    End With
End Sub

' Setup Settings sheet with dropdown lists
Sub SetupSettingsSheet()
    Dim ws As Worksheet
    Set ws = GetOrCreateSheet(SETTINGS_SHEET)
    
    With ws
        .Cells.Clear
        
        ' Account types
        .Cells(1, 1) = "Accounts:"
        .Cells(2, 1) = "Tumuga"
        .Cells(3, 1) = "Choice Grade"
        
        ' Categories
        .Cells(1, 3) = "Categories:"
        .Cells(2, 3) = "Repairs and Maintenance"
        .Cells(3, 3) = "Office Supplies"
        .Cells(4, 3) = "Fuel"
        .Cells(5, 3) = "Insurance"
        .Cells(6, 3) = "Professional Services"
        .Cells(7, 3) = "Equipment"
        .Cells(8, 3) = "Utilities"
        
        ' VAT Categories
        .Cells(1, 5) = "VAT Categories:"
        .Cells(2, 5) = "Standard Rate (15%)"
        .Cells(3, 5) = "Zero Rate (0%)"
        .Cells(4, 5) = "Exempt"
        .Cells(5, 5) = "Input VAT"
        .Cells(6, 5) = "Output VAT"
        
        .Columns.AutoFit
    End With
End Sub

' Helper function to get or create a worksheet
Function GetOrCreateSheet(sheetName As String) As Worksheet
    Dim ws As Worksheet
    
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(sheetName)
    On Error GoTo 0
    
    If ws Is Nothing Then
        Set ws = ThisWorkbook.Worksheets.Add
        ws.Name = sheetName
    End If
    
    Set GetOrCreateSheet = ws
End Function

' Auto-calculate item numbers based on document numbers
Sub CalculateItemNumbers()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim currentDoc As String
    Dim previousDoc As String
    Dim itemCounter As Integer
    
    Set ws = ThisWorkbook.Worksheets(INVOICE_SHEET)
    lastRow = ws.Cells(ws.Rows.Count, COL_INVOICE_NUMBER).End(xlUp).Row
    
    If lastRow < 2 Then Exit Sub
    
    itemCounter = 1
    previousDoc = ""
    
    For i = 2 To lastRow
        ' Get the document number (prioritize Invoice, then Quote, then Delivery Note)
        currentDoc = ""
        If ws.Cells(i, COL_INVOICE_NUMBER).Value <> "" Then
            currentDoc = ws.Cells(i, COL_INVOICE_NUMBER).Value
        ElseIf ws.Cells(i, COL_QUOTE_NUMBER).Value <> "" Then
            currentDoc = ws.Cells(i, COL_QUOTE_NUMBER).Value
        ElseIf ws.Cells(i, COL_DELIVERY_NOTE).Value <> "" Then
            currentDoc = ws.Cells(i, COL_DELIVERY_NOTE).Value
        End If
        
        If currentDoc <> previousDoc Then
            itemCounter = 1
        End If
        
        ws.Cells(i, COL_ITEM_NUMBER).Value = itemCounter
        
        If currentDoc = previousDoc Then
            itemCounter = itemCounter + 1
        End If
        
        previousDoc = currentDoc
    Next i
End Sub
