// Power Query M code for Invoice Data Transformation
// This query can be used to create a dynamic table that automatically updates

let
    // Get data from the Invoice Data sheet
    Source = Excel.CurrentWorkbook(){[Name="Invoice_Data"]}[Content],
    
    // Promote headers
    #"Promoted Headers" = Table.PromoteHeaders(Source, [PromoteAllScalars=true]),
    
    // Change data types
    #"Changed Type" = Table.TransformColumnTypes(#"Promoted Headers",{
        {"Item Number", Int64.Type},
        {"Supplier Name", type text},
        {"Delivery Note Number", type text},
        {"Invoice Number", type text},
        {"Quote Number", type text},
        {"Account", type text},
        {"Date", type date},
        {"Category", type text},
        {"Description", type text},
        {"Amount Excl VAT", Currency.Type},
        {"Amount Incl VAT", Currency.Type},
        {"VAT Amount", Currency.Type},
        {"VAT Category", type text},
        {"Document Folder Path", type text}
    }),
    
    // Filter out empty rows
    #"Filtered Rows" = Table.SelectRows(#"Changed Type", each [Supplier Name] <> null and [Supplier Name] <> ""),
    
    // Add calculated columns
    #"Added Document Type" = Table.AddColumn(#"Filtered Rows", "Document Type", each 
        if [Invoice Number] <> null and [Invoice Number] <> "" then "Invoice"
        else if [Quote Number] <> null and [Quote Number] <> "" then "Quote"
        else if [Delivery Note Number] <> null and [Delivery Note Number] <> "" then "Delivery Note"
        else "Unknown"
    ),
    
    #"Added Primary Document Number" = Table.AddColumn(#"Added Document Type", "Primary Document Number", each 
        if [Invoice Number] <> null and [Invoice Number] <> "" then [Invoice Number]
        else if [Quote Number] <> null and [Quote Number] <> "" then [Quote Number]
        else if [Delivery Note Number] <> null and [Delivery Note Number] <> "" then [Delivery Note Number]
        else ""
    ),
    
    #"Added Balance Impact" = Table.AddColumn(#"Added Primary Document Number", "Balance Impact", each 
        if [Document Type] = "Invoice" then "Debit"
        else "No Impact"
    ),
    
    #"Added Month Year" = Table.AddColumn(#"Added Balance Impact", "Month Year", each 
        Date.ToText([Date], "MMM yyyy")
    ),
    
    #"Added Quarter" = Table.AddColumn(#"Added Month Year", "Quarter", each 
        "Q" & Text.From(Date.QuarterOfYear([Date])) & " " & Text.From(Date.Year([Date]))
    ),
    
    // Add VAT validation
    #"Added VAT Validation" = Table.AddColumn(#"Added Quarter", "VAT Check", each 
        if [VAT Category] = "Standard Rate (15%)" then
            if Number.Round([Amount Excl VAT] * 0.15, 2) = Number.Round([VAT Amount], 2) then "Correct"
            else "Incorrect"
        else if [VAT Category] = "Zero Rate (0%)" or [VAT Category] = "Exempt" then
            if [VAT Amount] = 0 then "Correct"
            else "Incorrect"
        else "Unknown"
    )

in
    #"Added VAT Validation"

// Power Query M code for Supplier Summary
// This creates a summary table by supplier

let
    // Reference the transformed invoice data
    InvoiceData = #"Invoice_Data_Transformed",
    
    // Group by supplier and account
    #"Grouped Rows" = Table.Group(InvoiceData, {"Supplier Name", "Account"}, {
        {"Total Invoices", each Table.RowCount(Table.SelectRows(_, each [Document Type] = "Invoice")), Int64.Type},
        {"Total Invoice Amount Excl", each List.Sum(Table.SelectRows(_, each [Document Type] = "Invoice")[Amount Excl VAT]), Currency.Type},
        {"Total Invoice Amount Incl", each List.Sum(Table.SelectRows(_, each [Document Type] = "Invoice")[Amount Incl VAT]), Currency.Type},
        {"Total VAT Amount", each List.Sum(Table.SelectRows(_, each [Document Type] = "Invoice")[VAT Amount]), Currency.Type},
        {"Total Quotes", each Table.RowCount(Table.SelectRows(_, each [Document Type] = "Quote")), Int64.Type},
        {"Total Quote Amount", each List.Sum(Table.SelectRows(_, each [Document Type] = "Quote")[Amount Incl VAT]), Currency.Type},
        {"Last Invoice Date", each List.Max(Table.SelectRows(_, each [Document Type] = "Invoice")[Date]), type date},
        {"First Invoice Date", each List.Min(Table.SelectRows(_, each [Document Type] = "Invoice")[Date]), type date}
    }),
    
    // Add calculated fields
    #"Added Average Invoice" = Table.AddColumn(#"Grouped Rows", "Average Invoice Amount", each 
        if [Total Invoices] > 0 then [Total Invoice Amount Incl] / [Total Invoices] else 0
    ),
    
    #"Added Days Since Last Invoice" = Table.AddColumn(#"Added Average Invoice", "Days Since Last Invoice", each 
        if [Last Invoice Date] <> null then Duration.Days(DateTime.Date(DateTime.LocalNow()) - [Last Invoice Date]) else null
    )

in
    #"Added Days Since Last Invoice"

// Power Query M code for Monthly Summary
// This creates a monthly breakdown of expenses

let
    // Reference the transformed invoice data
    InvoiceData = #"Invoice_Data_Transformed",
    
    // Filter for invoices only
    #"Invoices Only" = Table.SelectRows(InvoiceData, each [Document Type] = "Invoice"),
    
    // Group by Month Year, Category, and Account
    #"Grouped by Month" = Table.Group(#"Invoices Only", {"Month Year", "Category", "Account"}, {
        {"Invoice Count", each Table.RowCount(_), Int64.Type},
        {"Total Amount Excl", each List.Sum([Amount Excl VAT]), Currency.Type},
        {"Total Amount Incl", each List.Sum([Amount Incl VAT]), Currency.Type},
        {"Total VAT", each List.Sum([VAT Amount]), Currency.Type}
    }),
    
    // Add date for sorting
    #"Added Sort Date" = Table.AddColumn(#"Grouped by Month", "Sort Date", each 
        Date.FromText("01 " & [Month Year])
    ),
    
    // Sort by date
    #"Sorted Rows" = Table.Sort(#"Added Sort Date",{{"Sort Date", Order.Descending}}),
    
    // Remove sort date column
    #"Removed Sort Date" = Table.RemoveColumns(#"Sorted Rows",{"Sort Date"})

in
    #"Removed Sort Date"

// Power Query M code for VAT Report
// This creates a VAT summary report

let
    // Reference the transformed invoice data
    InvoiceData = #"Invoice_Data_Transformed",
    
    // Filter for invoices only
    #"Invoices Only" = Table.SelectRows(InvoiceData, each [Document Type] = "Invoice"),
    
    // Group by VAT Category and Month Year
    #"Grouped by VAT" = Table.Group(#"Invoices Only", {"VAT Category", "Month Year"}, {
        {"Transaction Count", each Table.RowCount(_), Int64.Type},
        {"Total Taxable Amount", each List.Sum([Amount Excl VAT]), Currency.Type},
        {"Total VAT Amount", each List.Sum([VAT Amount]), Currency.Type},
        {"Total Amount Incl VAT", each List.Sum([Amount Incl VAT]), Currency.Type}
    }),
    
    // Add VAT rate calculation
    #"Added VAT Rate" = Table.AddColumn(#"Grouped by VAT", "Effective VAT Rate", each 
        if [Total Taxable Amount] > 0 then [Total VAT Amount] / [Total Taxable Amount] else 0
    ),
    
    // Format VAT rate as percentage
    #"Changed VAT Rate Type" = Table.TransformColumnTypes(#"Added VAT Rate",{{"Effective VAT Rate", Percentage.Type}})

in
    #"Changed VAT Rate Type"

// Power Query M code for Outstanding Invoices
// This identifies invoices that may need follow-up

let
    // Reference the transformed invoice data
    InvoiceData = #"Invoice_Data_Transformed",
    
    // Filter for invoices only
    #"Invoices Only" = Table.SelectRows(InvoiceData, each [Document Type] = "Invoice"),
    
    // Add age calculation
    #"Added Age" = Table.AddColumn(#"Invoices Only", "Invoice Age (Days)", each 
        Duration.Days(DateTime.Date(DateTime.LocalNow()) - [Date])
    ),
    
    // Add aging categories
    #"Added Aging Category" = Table.AddColumn(#"Added Age", "Aging Category", each 
        if [Invoice Age (Days)] <= 30 then "0-30 days"
        else if [Invoice Age (Days)] <= 60 then "31-60 days"
        else if [Invoice Age (Days)] <= 90 then "61-90 days"
        else "90+ days"
    ),
    
    // Select relevant columns
    #"Selected Columns" = Table.SelectColumns(#"Added Aging Category",{
        "Supplier Name", "Invoice Number", "Date", "Amount Incl VAT", 
        "Invoice Age (Days)", "Aging Category", "Category", "Account"
    }),
    
    // Sort by age descending
    #"Sorted by Age" = Table.Sort(#"Selected Columns",{{"Invoice Age (Days)", Order.Descending}})

in
    #"Sorted by Age"
