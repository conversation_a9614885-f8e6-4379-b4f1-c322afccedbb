# Quick Setup Instructions - Invoice Management System

## Step 1: Create the Excel File
1. Open Microsoft Excel
2. Create a new blank workbook
3. Save it as "Invoice_Management_System.xlsm" (Excel Macro-Enabled Workbook)

## Step 2: Create the Worksheets
Rename and create sheets so you have these 4 tabs:
1. **Invoice Data** (rename Sheet1)
2. **Accounts** (add new sheet)
3. **Bank Statements** (add new sheet)  
4. **Settings** (add new sheet)

## Step 3: Set Up Invoice Data Sheet
Copy and paste this header row into row 1 of the "Invoice Data" sheet:

```
Item Number	Supplier Name	Delivery Note Number	Invoice Number	Quote Number	Account	Date	Category	Description	Amount Excl VAT	Amount Incl VAT	VAT Amount	VAT Category	Document Folder Path
```

**Format the headers:**
- Select row 1
- Make it bold
- Set background color to blue
- Set text color to white
- Center align the text

## Step 4: Set Up Accounts Sheet
In the "Accounts" sheet, set up the filter area:

**Row 1:**
- A1: "Supplier Filter:"
- C1: "Date From:"
- E1: "Date To:"

**Row 4 (Headers for filtered data):**
```
Supplier Name	Document Type	Document Number	Date	Description	Amount Excl	Amount Incl	Balance Impact	Open Folder
```

## Step 5: Set Up Bank Statements Sheet
In the "Bank Statements" sheet, add these headers in row 1:

```
Date	Description	Reference	Amount	Supplier	Account	Category
```

## Step 6: Set Up Settings Sheet
In the "Settings" sheet, create these lists:

**Column A (Accounts):**
- A1: "Accounts:"
- A2: "Tumuga"
- A3: "Choice Grade"

**Column C (Categories):**
- C1: "Categories:"
- C2: "Repairs and Maintenance"
- C3: "Office Supplies"
- C4: "Fuel"
- C5: "Insurance"
- C6: "Professional Services"
- C7: "Equipment"
- C8: "Utilities"

**Column E (VAT Categories):**
- E1: "VAT Categories:"
- E2: "Standard Rate (15%)"
- E3: "Zero Rate (0%)"
- E4: "Exempt"
- E5: "Input VAT"
- E6: "Output VAT"

## Step 7: Add Data Validation (Dropdowns)
1. **Account Column (F) in Invoice Data:**
   - Select column F
   - Go to Data > Data Validation
   - Allow: List
   - Source: `Tumuga,Choice Grade`

2. **Category Column (H) in Invoice Data:**
   - Select column H
   - Data > Data Validation
   - Allow: List
   - Source: `Repairs and Maintenance,Office Supplies,Fuel,Insurance,Professional Services,Equipment,Utilities`

3. **VAT Category Column (M) in Invoice Data:**
   - Select column M
   - Data > Data Validation
   - Allow: List
   - Source: `Standard Rate (15%),Zero Rate (0%),Exempt,Input VAT,Output VAT`

## Step 8: Format Currency Columns
1. Select columns J, K, L in Invoice Data sheet
2. Right-click > Format Cells
3. Choose Currency
4. Set symbol to "R" and format as "R #,##0.00"

## Step 9: Import Sample Data (Optional)
You can copy the data from `Sample_Data.csv` and paste it into your Invoice Data sheet starting from row 2.

## Step 10: Add VBA Code (Advanced)
If you want the automation features:
1. Press Alt+F11 to open VBA Editor
2. Import the .bas files I provided
3. Follow the detailed instructions in the Setup Guide

## Basic Usage Without VBA
Even without the VBA code, you can use the system effectively:

1. **Enter invoice data** in the Invoice Data sheet
2. **Calculate VAT manually:**
   - For 15% VAT: Amount Excl × 0.15 = VAT Amount
   - Amount Incl = Amount Excl + VAT Amount
3. **Use filters** in Excel to view data by supplier or date
4. **Track payments** in the Bank Statements sheet

## Simple Formulas You Can Add
If you want some automation without VBA, add these formulas:

**Auto-calculate VAT (in column L):**
```
=IF(M2="Standard Rate (15%)",J2*0.15,0)
```

**Auto-calculate Amount Incl VAT (in column K):**
```
=J2+L2
```

**Item numbering (in column A):**
```
=IF(D2=D1,A1+1,1)
```

This gives you a functional invoice management system that you can start using immediately, even without the advanced VBA features!
