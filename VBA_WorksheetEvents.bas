Attribute VB_Name = "WorksheetEvents"
Option Explicit

' This code should be placed in the "Invoice Data" worksheet module
' To access: Right-click on "Invoice Data" sheet tab -> View Code

Private Sub Worksheet_Change(ByVal Target As Range)
    Application.EnableEvents = False
    
    On Error GoTo ErrorHandler
    
    ' Auto-calculate VAT when amounts or VAT category changes
    If Not Intersect(Target, Range("J:M")) Is Nothing Then
        Call CalculateVATAmount(Target)
    End If
    
    ' Auto-calculate item numbers when document numbers change
    If Not Intersect(Target, Range("C:E")) Is Nothing Then
        Call CalculateItemNumbers
    End If
    
    ' Update supplier dropdown when new suppliers are added
    If Not Intersect(Target, Columns(COL_SUPPLIER_NAME)) Is Nothing Then
        Call CreateSupplierDropdown
    End If
    
    ' Browse for folder when document path cell is double-clicked
    If Target.Column = COL_DOCUMENT_PATH And Target.Cells.Count = 1 Then
        If Target.Value = "" Or MsgBox("Browse for new folder?", vbYesNo) = vbYes Then
            Call BrowseFolderPath(Target)
        End If
    End If

ErrorHandler:
    Application.EnableEvents = True
    If Err.Number <> 0 Then
        MsgBox "Error in worksheet change event: " & Err.Description
    End If
End Sub

Private Sub Worksheet_SelectionChange(ByVal Target As Range)
    ' Highlight current row for better visibility
    Cells.Interior.ColorIndex = xlNone
    
    If Target.Row > 1 Then
        Range(Cells(Target.Row, 1), Cells(Target.Row, COL_DOCUMENT_PATH)).Interior.Color = RGB(230, 230, 250)
    End If
End Sub

' This code should be placed in the "Accounts" worksheet module
' To access: Right-click on "Accounts" sheet tab -> View Code

Private Sub Worksheet_Change_Accounts(ByVal Target As Range)
    Application.EnableEvents = False
    
    On Error GoTo ErrorHandler
    
    ' Auto-refresh when filter values change
    If Not Intersect(Target, Range("B2,D2,F2")) Is Nothing Then
        ' Small delay to allow user to finish typing
        Application.OnTime Now + TimeValue("00:00:01"), "RefreshAccountsData"
    End If

ErrorHandler:
    Application.EnableEvents = True
    If Err.Number <> 0 Then
        MsgBox "Error in accounts worksheet change event: " & Err.Description
    End If
End Sub

Private Sub Worksheet_BeforeDoubleClick_Accounts(ByVal Target As Range, Cancel As Boolean)
    ' Handle double-click on "Open Folder" cells
    If Target.Column = 9 And Target.Row >= 5 Then
        If Target.Value = "Open Folder" Then
            Dim folderPath As String
            Dim docRow As Long
            
            docRow = Target.Row
            
            ' Find the corresponding folder path from the invoice data
            Dim wsInvoice As Worksheet
            Set wsInvoice = ThisWorkbook.Worksheets(INVOICE_SHEET)
            
            Dim supplierName As String
            Dim docNumber As String
            Dim lastRow As Long
            Dim i As Long
            
            supplierName = Cells(docRow, 1).Value
            docNumber = Cells(docRow, 3).Value
            
            lastRow = wsInvoice.Cells(wsInvoice.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
            
            For i = 2 To lastRow
                If wsInvoice.Cells(i, COL_SUPPLIER_NAME).Value = supplierName And _
                   (wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value = docNumber Or _
                    wsInvoice.Cells(i, COL_QUOTE_NUMBER).Value = docNumber Or _
                    wsInvoice.Cells(i, COL_DELIVERY_NOTE).Value = docNumber) Then
                    
                    folderPath = wsInvoice.Cells(i, COL_DOCUMENT_PATH).Value
                    Exit For
                End If
            Next i
            
            If folderPath <> "" Then
                Call OpenDocumentFolder(folderPath)
            Else
                MsgBox "No folder path found for this document.", vbExclamation
            End If
            
            Cancel = True
        End If
    End If
End Sub

' This code should be placed in the ThisWorkbook module
' To access: In VBA Editor, double-click on "ThisWorkbook"

Private Sub Workbook_Open()
    ' Initialize the workbook when opened
    Call FormatCurrencyCells
    Call CreateDataValidation
    Call CreateActionButtons
    
    ' Welcome message
    MsgBox "Welcome to the Invoice Management System!" & vbCrLf & vbCrLf & _
           "Features:" & vbCrLf & _
           "• Track invoices, quotes, and delivery notes" & vbCrLf & _
           "• Filter by supplier and date range" & vbCrLf & _
           "• Open document folders directly" & vbCrLf & _
           "• Automatic VAT calculations" & vbCrLf & _
           "• Supplier balance tracking", vbInformation, "Invoice Management System"
End Sub

Private Sub Workbook_BeforeClose(Cancel As Boolean)
    ' Clean up and save
    Dim response As VbMsgBoxResult
    
    response = MsgBox("Do you want to save the Invoice Management System?", _
                     vbYesNoCancel + vbQuestion, "Save Workbook")
    
    Select Case response
        Case vbYes
            ThisWorkbook.Save
        Case vbCancel
            Cancel = True
        Case vbNo
            ' Don't save, just close
    End Select
End Sub

' Auto-save every 10 minutes
Private Sub Workbook_SheetChange(ByVal Sh As Object, ByVal Target As Range)
    Static lastSave As Date
    
    ' Auto-save every 10 minutes
    If DateDiff("n", lastSave, Now) >= 10 Then
        ThisWorkbook.Save
        lastSave = Now
    End If
End Sub
