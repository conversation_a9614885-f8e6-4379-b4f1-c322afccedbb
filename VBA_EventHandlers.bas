Attribute VB_Name = "EventHandlers"
Option Explicit

' Create data validation dropdowns
Sub CreateDataValidation()
    Dim wsInvoice As Worksheet
    Dim wsSettings As Worksheet
    Dim wsAccounts As Worksheet
    Dim lastRow As Long
    
    Set wsInvoice = ThisWorkbook.Worksheets(INVOICE_SHEET)
    Set wsSettings = ThisWorkbook.Worksheets(SETTINGS_SHEET)
    Set wsAccounts = ThisWorkbook.Worksheets(ACCOUNTS_SHEET)
    
    ' Account validation (Tumuga, Choice Grade)
    With wsInvoice.Range("F:F")
        .Validation.Delete
        .Validation.Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
                        Formula1:="Tumuga,Choice Grade"
        .Validation.IgnoreBlank = True
        .Validation.InCellDropdown = True
    End With
    
    ' Category validation
    With wsInvoice.Range("H:H")
        .Validation.Delete
        .Validation.Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
                        Formula1:="Repairs and Maintenance,Office Supplies,Fuel,Insurance,Professional Services,Equipment,Utilities"
        .Validation.IgnoreBlank = True
        .Validation.InCellDropdown = True
    End With
    
    ' VAT Category validation
    With wsInvoice.Range("M:M")
        .Validation.Delete
        .Validation.Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
                        Formula1:="Standard Rate (15%),Zero Rate (0%),Exempt,Input VAT,Output VAT"
        .Validation.IgnoreBlank = True
        .Validation.InCellDropdown = True
    End With
    
    ' Date validation
    With wsInvoice.Range("G:G")
        .Validation.Delete
        .Validation.Add Type:=xlValidateDate, AlertStyle:=xlValidAlertStop, _
                        Operator:=xlBetween, Formula1:="1/1/2020", Formula2:="12/31/2030"
        .Validation.IgnoreBlank = True
    End With
    
    ' Supplier dropdown for Accounts sheet filter
    CreateSupplierDropdown
    
    ' Date validation for Accounts sheet filters
    With wsAccounts.Range("D2")
        .Validation.Delete
        .Validation.Add Type:=xlValidateDate, AlertStyle:=xlValidAlertStop, _
                        Operator:=xlBetween, Formula1:="1/1/2020", Formula2:="12/31/2030"
        .Validation.IgnoreBlank = True
    End With
    
    With wsAccounts.Range("F2")
        .Validation.Delete
        .Validation.Add Type:=xlValidateDate, AlertStyle:=xlValidAlertStop, _
                        Operator:=xlBetween, Formula1:="1/1/2020", Formula2:="12/31/2030"
        .Validation.IgnoreBlank = True
    End With
End Sub

' Create dynamic supplier dropdown
Sub CreateSupplierDropdown()
    Dim wsInvoice As Worksheet
    Dim wsAccounts As Worksheet
    Dim suppliers As Collection
    Dim supplier As Variant
    Dim supplierList As String
    Dim lastRow As Long
    Dim i As Long
    
    Set wsInvoice = ThisWorkbook.Worksheets(INVOICE_SHEET)
    Set wsAccounts = ThisWorkbook.Worksheets(ACCOUNTS_SHEET)
    Set suppliers = New Collection
    
    ' Get unique suppliers
    lastRow = wsInvoice.Cells(wsInvoice.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    
    For i = 2 To lastRow
        Dim supplierName As String
        supplierName = wsInvoice.Cells(i, COL_SUPPLIER_NAME).Value
        
        If supplierName <> "" Then
            On Error Resume Next
            suppliers.Add supplierName, supplierName
            On Error GoTo 0
        End If
    Next i
    
    ' Build supplier list string
    supplierList = ""
    For Each supplier In suppliers
        If supplierList = "" Then
            supplierList = supplier
        Else
            supplierList = supplierList & "," & supplier
        End If
    Next supplier
    
    ' Apply validation to supplier filter cell
    If supplierList <> "" Then
        With wsAccounts.Range("B2")
            .Validation.Delete
            .Validation.Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
                            Formula1:=supplierList
            .Validation.IgnoreBlank = True
            .Validation.InCellDropdown = True
        End With
    End If
End Sub

' Auto-calculate VAT amount when amounts are entered
Sub CalculateVATAmount(targetCell As Range)
    Dim ws As Worksheet
    Dim row As Long
    Dim amountExcl As Double
    Dim amountIncl As Double
    Dim vatAmount As Double
    Dim vatCategory As String
    
    Set ws = targetCell.Worksheet
    row = targetCell.row
    
    If ws.Name <> INVOICE_SHEET Or row < 2 Then Exit Sub
    
    amountExcl = ws.Cells(row, COL_AMOUNT_EXL).Value
    amountIncl = ws.Cells(row, COL_AMOUNT_INCL).Value
    vatCategory = ws.Cells(row, COL_VAT_CATEGORY).Value
    
    ' Calculate VAT amount based on available data
    If amountExcl > 0 And amountIncl > 0 Then
        vatAmount = amountIncl - amountExcl
        ws.Cells(row, COL_VAT_AMOUNT).Value = vatAmount
    ElseIf amountExcl > 0 And vatCategory <> "" Then
        ' Calculate based on VAT category
        Select Case vatCategory
            Case "Standard Rate (15%)"
                vatAmount = amountExcl * 0.15
                ws.Cells(row, COL_VAT_AMOUNT).Value = vatAmount
                ws.Cells(row, COL_AMOUNT_INCL).Value = amountExcl + vatAmount
            Case "Zero Rate (0%)", "Exempt"
                vatAmount = 0
                ws.Cells(row, COL_VAT_AMOUNT).Value = vatAmount
                ws.Cells(row, COL_AMOUNT_INCL).Value = amountExcl
        End Select
    ElseIf amountIncl > 0 And vatCategory <> "" Then
        ' Calculate backwards from inclusive amount
        Select Case vatCategory
            Case "Standard Rate (15%)"
                amountExcl = amountIncl / 1.15
                vatAmount = amountIncl - amountExcl
                ws.Cells(row, COL_AMOUNT_EXL).Value = amountExcl
                ws.Cells(row, COL_VAT_AMOUNT).Value = vatAmount
            Case "Zero Rate (0%)", "Exempt"
                vatAmount = 0
                ws.Cells(row, COL_VAT_AMOUNT).Value = vatAmount
                ws.Cells(row, COL_AMOUNT_EXL).Value = amountIncl
        End Select
    End If
End Sub

' Format currency cells
Sub FormatCurrencyCells()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets(INVOICE_SHEET)
    
    ' Format amount columns as currency
    ws.Columns(COL_AMOUNT_EXL).NumberFormat = "R #,##0.00"
    ws.Columns(COL_AMOUNT_INCL).NumberFormat = "R #,##0.00"
    ws.Columns(COL_VAT_AMOUNT).NumberFormat = "R #,##0.00"
    
    ' Format date column
    ws.Columns(COL_DATE).NumberFormat = "dd/mm/yyyy"
End Sub

' Add new invoice row with proper formatting
Sub AddNewInvoiceRow()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim newRow As Long
    
    Set ws = ThisWorkbook.Worksheets(INVOICE_SHEET)
    lastRow = ws.Cells(ws.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    newRow = lastRow + 1
    
    ' Add borders to new row
    With ws.Range(ws.Cells(newRow, 1), ws.Cells(newRow, COL_DOCUMENT_PATH))
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With
    
    ' Set default values
    ws.Cells(newRow, COL_DATE).Value = Date
    ws.Cells(newRow, COL_ACCOUNT).Value = "Tumuga" ' Default account
    
    ' Select the supplier name cell for data entry
    ws.Cells(newRow, COL_SUPPLIER_NAME).Select
    
    Call FormatCurrencyCells
End Sub

' Browse for folder path
Sub BrowseFolderPath(targetCell As Range)
    Dim folderPath As String
    
    With Application.FileDialog(msoFileDialogFolderPicker)
        .Title = "Select Document Folder"
        .AllowMultiSelect = False
        
        If .Show = -1 Then
            folderPath = .SelectedItems(1)
            targetCell.Value = folderPath
        End If
    End With
End Sub

' Create buttons for common actions
Sub CreateActionButtons()
    Dim wsInvoice As Worksheet
    Dim wsAccounts As Worksheet
    
    Set wsInvoice = ThisWorkbook.Worksheets(INVOICE_SHEET)
    Set wsAccounts = ThisWorkbook.Worksheets(ACCOUNTS_SHEET)
    
    ' Add buttons to Invoice sheet
    AddButton wsInvoice, "Add New Row", "AddNewInvoiceRow", 1, 16, 100, 25
    AddButton wsInvoice, "Calculate Items", "CalculateItemNumbers", 1, 18, 100, 25
    AddButton wsInvoice, "Update Suppliers", "CreateSupplierDropdown", 1, 20, 100, 25
    
    ' Add buttons to Accounts sheet
    AddButton wsAccounts, "Refresh Data", "RefreshAccountsData", 2, 7, 100, 25
    AddButton wsAccounts, "Supplier Summary", "CreateSupplierSummary", 2, 9, 120, 25
End Sub

' Helper function to add buttons
Sub AddButton(ws As Worksheet, buttonText As String, macroName As String, _
              topRow As Long, leftCol As Long, width As Double, height As Double)
    
    Dim btn As Button
    
    ' Delete existing button if it exists
    On Error Resume Next
    ws.Buttons(buttonText).Delete
    On Error GoTo 0
    
    ' Create new button
    Set btn = ws.Buttons.Add(ws.Cells(topRow, leftCol).Left, _
                            ws.Cells(topRow, leftCol).Top, width, height)
    
    With btn
        .Text = buttonText
        .OnAction = macroName
        .Font.Size = 10
        .Font.Bold = True
    End With
End Sub
