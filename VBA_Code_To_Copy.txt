========================================
COPY THIS CODE INTO A NEW MODULE
========================================
1. In Excel, press Alt+F11 to open VBA Editor
2. Right-click in Project Explorer > Insert > Module
3. Copy and paste ALL the code below:

Option Explicit

' Column constants
Public Const COL_ITEM_NUMBER = 1
Public Const COL_SUPPLIER_NAME = 2
Public Const COL_DELIVERY_NOTE = 3
Public Const COL_INVOICE_NUMBER = 4
Public Const COL_QUOTE_NUMBER = 5
Public Const COL_ACCOUNT = 6
Public Const COL_DATE = 7
Public Const COL_CATEGORY = 8
Public Const COL_DESCRIPTION = 9
Public Const COL_AMOUNT_EXL = 10
Public Const COL_AMOUNT_INCL = 11
Public Const COL_VAT_AMOUNT = 12
Public Const COL_VAT_CATEGORY = 13
Public Const COL_DOCUMENT_PATH = 14

' Auto-calculate item numbers based on document numbers
Sub CalculateItemNumbers()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim currentDoc As String, previousDoc As String
    Dim itemCounter As Integer
    
    Set ws = ThisWorkbook.Worksheets("Invoice Data")
    lastRow = ws.Cells(ws.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    
    If lastRow < 2 Then Exit Sub
    
    itemCounter = 1
    previousDoc = ""
    
    For i = 2 To lastRow
        ' Get the document number (prioritize Invoice, then Quote, then Delivery Note)
        currentDoc = ""
        If ws.Cells(i, COL_INVOICE_NUMBER).Value <> "" Then
            currentDoc = ws.Cells(i, COL_INVOICE_NUMBER).Value
        ElseIf ws.Cells(i, COL_QUOTE_NUMBER).Value <> "" Then
            currentDoc = ws.Cells(i, COL_QUOTE_NUMBER).Value
        ElseIf ws.Cells(i, COL_DELIVERY_NOTE).Value <> "" Then
            currentDoc = ws.Cells(i, COL_DELIVERY_NOTE).Value
        End If
        
        If currentDoc <> previousDoc Then itemCounter = 1
        
        ws.Cells(i, COL_ITEM_NUMBER).Value = itemCounter
        
        If currentDoc = previousDoc Then itemCounter = itemCounter + 1
        previousDoc = currentDoc
    Next i
End Sub

' Auto-calculate VAT amounts
Sub CalculateVAT(targetRow As Long)
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("Invoice Data")
    
    Dim amountExcl As Double, vatCategory As String
    amountExcl = ws.Cells(targetRow, COL_AMOUNT_EXL).Value
    vatCategory = ws.Cells(targetRow, COL_VAT_CATEGORY).Value
    
    If amountExcl > 0 And vatCategory <> "" Then
        Select Case vatCategory
            Case "Standard Rate (15%)"
                ws.Cells(targetRow, COL_VAT_AMOUNT).Value = Round(amountExcl * 0.15, 2)
                ws.Cells(targetRow, COL_AMOUNT_INCL).Value = Round(amountExcl * 1.15, 2)
            Case "Zero Rate (0%)", "Exempt"
                ws.Cells(targetRow, COL_VAT_AMOUNT).Value = 0
                ws.Cells(targetRow, COL_AMOUNT_INCL).Value = amountExcl
        End Select
    End If
End Sub

' Refresh accounts data with filters
Sub RefreshAccountsData()
    Dim wsAccounts As Worksheet, wsInvoice As Worksheet, wsBank As Worksheet
    Set wsAccounts = ThisWorkbook.Worksheets("Accounts")
    Set wsInvoice = ThisWorkbook.Worksheets("Invoice Data")
    Set wsBank = ThisWorkbook.Worksheets("Bank Statements")
    
    Dim supplierFilter As String, dateFrom As Date, dateTo As Date
    supplierFilter = wsAccounts.Cells(2, 2).Value
    
    On Error Resume Next
    dateFrom = CDate(wsAccounts.Cells(2, 4).Value)
    dateTo = CDate(wsAccounts.Cells(2, 6).Value)
    On Error GoTo 0
    
    If dateFrom = 0 Then dateFrom = DateSerial(1900, 1, 1)
    If dateTo = 0 Then dateTo = DateSerial(2100, 12, 31)
    
    ' Clear existing data
    wsAccounts.Range("A5:I1000").Clear
    
    Dim lastRow As Long, outputRow As Long, i As Long
    lastRow = wsInvoice.Cells(wsInvoice.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    outputRow = 5
    
    ' Process invoice data
    For i = 2 To lastRow
        Dim supplierName As String, docDate As Date
        supplierName = wsInvoice.Cells(i, COL_SUPPLIER_NAME).Value
        
        On Error Resume Next
        docDate = CDate(wsInvoice.Cells(i, COL_DATE).Value)
        On Error GoTo 0
        
        ' Check filters
        If (supplierFilter = "" Or InStr(1, supplierName, supplierFilter, vbTextCompare) > 0) And _
           (docDate >= dateFrom And docDate <= dateTo) Then
            
            wsAccounts.Cells(outputRow, 1).Value = supplierName
            
            ' Determine document type
            If wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value <> "" Then
                wsAccounts.Cells(outputRow, 2).Value = "Invoice"
                wsAccounts.Cells(outputRow, 3).Value = wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value
                wsAccounts.Cells(outputRow, 8).Value = "Debit"
            ElseIf wsInvoice.Cells(i, COL_QUOTE_NUMBER).Value <> "" Then
                wsAccounts.Cells(outputRow, 2).Value = "Quote"
                wsAccounts.Cells(outputRow, 3).Value = wsInvoice.Cells(i, COL_QUOTE_NUMBER).Value
                wsAccounts.Cells(outputRow, 8).Value = "No Impact"
            ElseIf wsInvoice.Cells(i, COL_DELIVERY_NOTE).Value <> "" Then
                wsAccounts.Cells(outputRow, 2).Value = "Delivery Note"
                wsAccounts.Cells(outputRow, 3).Value = wsInvoice.Cells(i, COL_DELIVERY_NOTE).Value
                wsAccounts.Cells(outputRow, 8).Value = "No Impact"
            End If
            
            wsAccounts.Cells(outputRow, 4).Value = docDate
            wsAccounts.Cells(outputRow, 5).Value = wsInvoice.Cells(i, COL_DESCRIPTION).Value
            wsAccounts.Cells(outputRow, 6).Value = wsInvoice.Cells(i, COL_AMOUNT_EXL).Value
            wsAccounts.Cells(outputRow, 7).Value = wsInvoice.Cells(i, COL_AMOUNT_INCL).Value
            
            ' Add folder link if path exists
            If wsInvoice.Cells(i, COL_DOCUMENT_PATH).Value <> "" Then
                wsAccounts.Cells(outputRow, 9).Value = "Open Folder"
                wsAccounts.Cells(outputRow, 9).Interior.Color = RGB(144, 238, 144)
                wsAccounts.Cells(outputRow, 9).Font.Bold = True
            End If
            
            outputRow = outputRow + 1
        End If
    Next i
    
    ' Process bank payments
    lastRow = wsBank.Cells(wsBank.Rows.Count, 1).End(xlUp).Row
    For i = 2 To lastRow
        supplierName = wsBank.Cells(i, 5).Value
        
        On Error Resume Next
        docDate = CDate(wsBank.Cells(i, 1).Value)
        On Error GoTo 0
        
        If (supplierFilter = "" Or InStr(1, supplierName, supplierFilter, vbTextCompare) > 0) And _
           (docDate >= dateFrom And docDate <= dateTo) And supplierName <> "" Then
            
            wsAccounts.Cells(outputRow, 1).Value = supplierName
            wsAccounts.Cells(outputRow, 2).Value = "Payment"
            wsAccounts.Cells(outputRow, 3).Value = wsBank.Cells(i, 3).Value
            wsAccounts.Cells(outputRow, 4).Value = docDate
            wsAccounts.Cells(outputRow, 5).Value = wsBank.Cells(i, 2).Value
            wsAccounts.Cells(outputRow, 6).Value = wsBank.Cells(i, 4).Value
            wsAccounts.Cells(outputRow, 7).Value = wsBank.Cells(i, 4).Value
            wsAccounts.Cells(outputRow, 8).Value = "Credit"
            
            outputRow = outputRow + 1
        End If
    Next i
    
    ' Format the results
    If outputRow > 5 Then
        wsAccounts.Range("F5:G" & (outputRow - 1)).NumberFormat = "R #,##0.00"
        With wsAccounts.Range("A5:I" & (outputRow - 1))
            .Borders.LineStyle = xlContinuous
            .Borders.Weight = xlThin
        End With
    End If
    
    wsAccounts.Columns.AutoFit
    MsgBox "Accounts data refreshed successfully!", vbInformation
End Sub

' Add new invoice row
Sub AddNewRow()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("Invoice Data")
    
    Dim lastRow As Long
    lastRow = ws.Cells(ws.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    
    ' Set default values for new row
    ws.Cells(lastRow + 1, COL_DATE).Value = Date
    ws.Cells(lastRow + 1, COL_ACCOUNT).Value = "Tumuga"
    
    ' Select supplier name cell for data entry
    ws.Cells(lastRow + 1, COL_SUPPLIER_NAME).Select
End Sub

' Open document folder
Sub OpenFolder(folderPath As String)
    If folderPath <> "" And Dir(folderPath, vbDirectory) <> "" Then
        Shell "explorer.exe """ & folderPath & """", vbNormalFocus
    Else
        MsgBox "Folder path not found: " & folderPath, vbExclamation
    End If
End Sub

' Calculate supplier balance
Function GetSupplierBalance(supplierName As String, accountType As String) As Double
    Dim wsInvoice As Worksheet, wsBank As Worksheet
    Set wsInvoice = ThisWorkbook.Worksheets("Invoice Data")
    Set wsBank = ThisWorkbook.Worksheets("Bank Statements")
    
    Dim balance As Double, lastRow As Long, i As Long
    balance = 0
    
    ' Add invoice amounts (debits)
    lastRow = wsInvoice.Cells(wsInvoice.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    For i = 2 To lastRow
        If wsInvoice.Cells(i, COL_SUPPLIER_NAME).Value = supplierName And _
           wsInvoice.Cells(i, COL_ACCOUNT).Value = accountType And _
           wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value <> "" Then
            balance = balance + wsInvoice.Cells(i, COL_AMOUNT_INCL).Value
        End If
    Next i
    
    ' Subtract payments (credits)
    lastRow = wsBank.Cells(wsBank.Rows.Count, 1).End(xlUp).Row
    For i = 2 To lastRow
        If wsBank.Cells(i, 5).Value = supplierName And _
           wsBank.Cells(i, 6).Value = accountType Then
            balance = balance - wsBank.Cells(i, 4).Value
        End If
    Next i
    
    GetSupplierBalance = balance
End Function

========================================
COPY THIS CODE INTO INVOICE DATA SHEET
========================================
1. In VBA Editor, double-click "Sheet1 (Invoice Data)" in Project Explorer
2. Copy and paste this code:

Private Sub Worksheet_Change(ByVal Target As Range)
    Application.EnableEvents = False
    On Error GoTo ErrorHandler
    
    ' Auto-calculate VAT when amounts or VAT category changes
    If Not Intersect(Target, Range("J:M")) Is Nothing Then
        If Target.Row > 1 Then Call CalculateVAT(Target.Row)
    End If
    
    ' Auto-calculate item numbers when document numbers change
    If Not Intersect(Target, Range("C:E")) Is Nothing Then
        Call CalculateItemNumbers
    End If
    
ErrorHandler:
    Application.EnableEvents = True
End Sub

========================================
COPY THIS CODE INTO ACCOUNTS SHEET
========================================
1. In VBA Editor, double-click "Sheet2 (Accounts)" in Project Explorer
2. Copy and paste this code:

Private Sub Worksheet_BeforeDoubleClick(ByVal Target As Range, Cancel As Boolean)
    ' Handle double-click on "Open Folder" cells
    If Target.Column = 9 And Target.Row >= 5 Then
        If Target.Value = "Open Folder" Then
            Dim supplierName As String, docNumber As String, folderPath As String
            supplierName = Cells(Target.Row, 1).Value
            docNumber = Cells(Target.Row, 3).Value
            
            ' Find folder path from Invoice Data
            Dim ws As Worksheet
            Set ws = ThisWorkbook.Worksheets("Invoice Data")
            Dim i As Long
            
            For i = 2 To ws.Cells(ws.Rows.Count, 2).End(xlUp).Row
                If ws.Cells(i, 2).Value = supplierName And _
                   (ws.Cells(i, 4).Value = docNumber Or _
                    ws.Cells(i, 5).Value = docNumber Or _
                    ws.Cells(i, 3).Value = docNumber) Then
                    folderPath = ws.Cells(i, 14).Value
                    Exit For
                End If
            Next i
            
            If folderPath <> "" Then
                Call OpenFolder(folderPath)
            Else
                MsgBox "No folder path found for this document.", vbExclamation
            End If
            
            Cancel = True
        End If
    End If
End Sub

========================================
AFTER ADDING ALL CODE:
========================================
1. Close VBA Editor (Alt+Q)
2. Save your Excel file (Ctrl+S)
3. Test the system by:
   - Adding data to Invoice Data sheet
   - Running "CalculateItemNumbers" macro
   - Using the "Refresh Data" button on Accounts sheet
