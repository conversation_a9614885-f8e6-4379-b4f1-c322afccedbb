# Complete Invoice Management System Setup

## Your Excel File is Ready!
✅ `Invoice_Management_System.xlsm` - Fully formatted with all sheets and headers

## Quick Start (No VBA Required)
Your Excel file is immediately usable! You can:
1. Enter invoice data in the "Invoice Data" sheet
2. Use the "Accounts" sheet for filtering (manual)
3. Track payments in "Bank Statements" sheet
4. Reference categories from "Settings" sheet

## Add Automation (Optional VBA Setup)

### Step 1: Enable VBA Access
1. Open Excel
2. Go to File > Options > Trust Center > Trust Center Settings
3. Click "Macro Settings"
4. Select "Enable all macros"
5. Check "Trust access to the VBA project object model"
6. Click OK and restart Excel

### Step 2: Add Essential VBA Code
1. Open your Excel file
2. Press `Alt+F11` to open VBA Editor
3. Right-click in Project Explorer > Insert > Module
4. Copy and paste this essential code:

```vba
Option Explicit

' Column constants
Public Const COL_ITEM_NUMBER = 1
Public Const COL_SUPPLIER_NAME = 2
Public Const COL_DELIVERY_NOTE = 3
Public Const COL_INVOICE_NUMBER = 4
Public Const COL_QUOTE_NUMBER = 5
Public Const COL_ACCOUNT = 6
Public Const COL_DATE = 7
Public Const COL_CATEGORY = 8
Public Const COL_DESCRIPTION = 9
Public Const COL_AMOUNT_EXL = 10
Public Const COL_AMOUNT_INCL = 11
Public Const COL_VAT_AMOUNT = 12
Public Const COL_VAT_CATEGORY = 13
Public Const COL_DOCUMENT_PATH = 14

' Auto-calculate item numbers
Sub CalculateItemNumbers()
    Dim ws As Worksheet
    Dim lastRow As Long, i As Long
    Dim currentDoc As String, previousDoc As String
    Dim itemCounter As Integer
    
    Set ws = ThisWorkbook.Worksheets("Invoice Data")
    lastRow = ws.Cells(ws.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    
    If lastRow < 2 Then Exit Sub
    
    itemCounter = 1
    previousDoc = ""
    
    For i = 2 To lastRow
        currentDoc = ""
        If ws.Cells(i, COL_INVOICE_NUMBER).Value <> "" Then
            currentDoc = ws.Cells(i, COL_INVOICE_NUMBER).Value
        ElseIf ws.Cells(i, COL_QUOTE_NUMBER).Value <> "" Then
            currentDoc = ws.Cells(i, COL_QUOTE_NUMBER).Value
        ElseIf ws.Cells(i, COL_DELIVERY_NOTE).Value <> "" Then
            currentDoc = ws.Cells(i, COL_DELIVERY_NOTE).Value
        End If
        
        If currentDoc <> previousDoc Then itemCounter = 1
        
        ws.Cells(i, COL_ITEM_NUMBER).Value = itemCounter
        
        If currentDoc = previousDoc Then itemCounter = itemCounter + 1
        previousDoc = currentDoc
    Next i
End Sub

' Auto-calculate VAT
Sub CalculateVAT(targetRow As Long)
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("Invoice Data")
    
    Dim amountExcl As Double, vatCategory As String
    amountExcl = ws.Cells(targetRow, COL_AMOUNT_EXL).Value
    vatCategory = ws.Cells(targetRow, COL_VAT_CATEGORY).Value
    
    If amountExcl > 0 And vatCategory <> "" Then
        Select Case vatCategory
            Case "Standard Rate (15%)"
                ws.Cells(targetRow, COL_VAT_AMOUNT).Value = amountExcl * 0.15
                ws.Cells(targetRow, COL_AMOUNT_INCL).Value = amountExcl * 1.15
            Case "Zero Rate (0%)", "Exempt"
                ws.Cells(targetRow, COL_VAT_AMOUNT).Value = 0
                ws.Cells(targetRow, COL_AMOUNT_INCL).Value = amountExcl
        End Select
    End If
End Sub

' Refresh accounts data
Sub RefreshAccountsData()
    Dim wsAccounts As Worksheet, wsInvoice As Worksheet
    Set wsAccounts = ThisWorkbook.Worksheets("Accounts")
    Set wsInvoice = ThisWorkbook.Worksheets("Invoice Data")
    
    Dim supplierFilter As String, dateFrom As Date, dateTo As Date
    supplierFilter = wsAccounts.Cells(2, 2).Value
    
    On Error Resume Next
    dateFrom = CDate(wsAccounts.Cells(2, 4).Value)
    dateTo = CDate(wsAccounts.Cells(2, 6).Value)
    On Error GoTo 0
    
    If dateFrom = 0 Then dateFrom = DateSerial(1900, 1, 1)
    If dateTo = 0 Then dateTo = DateSerial(2100, 12, 31)
    
    ' Clear existing data
    wsAccounts.Range("A5:I1000").Clear
    
    Dim lastRow As Long, outputRow As Long, i As Long
    lastRow = wsInvoice.Cells(wsInvoice.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    outputRow = 5
    
    For i = 2 To lastRow
        Dim supplierName As String, docDate As Date
        supplierName = wsInvoice.Cells(i, COL_SUPPLIER_NAME).Value
        
        On Error Resume Next
        docDate = CDate(wsInvoice.Cells(i, COL_DATE).Value)
        On Error GoTo 0
        
        If (supplierFilter = "" Or InStr(1, supplierName, supplierFilter, vbTextCompare) > 0) And _
           (docDate >= dateFrom And docDate <= dateTo) Then
            
            wsAccounts.Cells(outputRow, 1).Value = supplierName
            
            If wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value <> "" Then
                wsAccounts.Cells(outputRow, 2).Value = "Invoice"
                wsAccounts.Cells(outputRow, 3).Value = wsInvoice.Cells(i, COL_INVOICE_NUMBER).Value
                wsAccounts.Cells(outputRow, 8).Value = "Debit"
            ElseIf wsInvoice.Cells(i, COL_QUOTE_NUMBER).Value <> "" Then
                wsAccounts.Cells(outputRow, 2).Value = "Quote"
                wsAccounts.Cells(outputRow, 3).Value = wsInvoice.Cells(i, COL_QUOTE_NUMBER).Value
                wsAccounts.Cells(outputRow, 8).Value = "No Impact"
            End If
            
            wsAccounts.Cells(outputRow, 4).Value = docDate
            wsAccounts.Cells(outputRow, 5).Value = wsInvoice.Cells(i, COL_DESCRIPTION).Value
            wsAccounts.Cells(outputRow, 6).Value = wsInvoice.Cells(i, COL_AMOUNT_EXL).Value
            wsAccounts.Cells(outputRow, 7).Value = wsInvoice.Cells(i, COL_AMOUNT_INCL).Value
            
            If wsInvoice.Cells(i, COL_DOCUMENT_PATH).Value <> "" Then
                wsAccounts.Cells(outputRow, 9).Value = "Open Folder"
            End If
            
            outputRow = outputRow + 1
        End If
    Next i
    
    MsgBox "Data refreshed successfully!", vbInformation
End Sub

' Add new invoice row
Sub AddNewRow()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("Invoice Data")
    
    Dim lastRow As Long
    lastRow = ws.Cells(ws.Rows.Count, COL_SUPPLIER_NAME).End(xlUp).Row
    
    ws.Cells(lastRow + 1, COL_DATE).Value = Date
    ws.Cells(lastRow + 1, COL_ACCOUNT).Value = "Tumuga"
    ws.Cells(lastRow + 1, COL_SUPPLIER_NAME).Select
End Sub
```

### Step 3: Add Data Validation
1. Still in VBA Editor, add this code to create dropdowns:

```vba
Sub SetupDataValidation()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("Invoice Data")
    
    ' Account dropdown
    With ws.Range("F:F")
        .Validation.Delete
        .Validation.Add Type:=xlValidateList, Formula1:="Tumuga,Choice Grade"
        .Validation.InCellDropdown = True
    End With
    
    ' Category dropdown
    With ws.Range("H:H")
        .Validation.Delete
        .Validation.Add Type:=xlValidateList, Formula1:="Repairs and Maintenance,Office Supplies,Fuel,Insurance,Professional Services,Equipment,Utilities"
        .Validation.InCellDropdown = True
    End With
    
    ' VAT Category dropdown
    With ws.Range("M:M")
        .Validation.Delete
        .Validation.Add Type:=xlValidateList, Formula1:="Standard Rate (15%),Zero Rate (0%),Exempt,Input VAT,Output VAT"
        .Validation.InCellDropdown = True
    End With
    
    MsgBox "Data validation setup complete!", vbInformation
End Sub
```

### Step 4: Add Worksheet Events (Optional)
1. In VBA Editor, double-click "Sheet1 (Invoice Data)" in Project Explorer
2. Add this code for automatic calculations:

```vba
Private Sub Worksheet_Change(ByVal Target As Range)
    Application.EnableEvents = False
    
    ' Auto-calculate VAT when amounts change
    If Not Intersect(Target, Range("J:M")) Is Nothing Then
        If Target.Row > 1 Then Call CalculateVAT(Target.Row)
    End If
    
    ' Auto-calculate item numbers when document numbers change
    If Not Intersect(Target, Range("C:E")) Is Nothing Then
        Call CalculateItemNumbers
    End If
    
    Application.EnableEvents = True
End Sub
```

### Step 5: Initialize the System
1. Close VBA Editor (Alt+Q)
2. Press Alt+F8 to open Macros
3. Run "SetupDataValidation" macro
4. Your system is now fully automated!

## How to Use Your System

### Adding Invoices:
1. Go to "Invoice Data" sheet
2. Run "AddNewRow" macro (Alt+F8) or manually go to next empty row
3. Enter supplier name, document numbers, amounts
4. VAT will calculate automatically (if VBA is set up)
5. Item numbers will update automatically

### Viewing Filtered Data:
1. Go to "Accounts" sheet
2. Enter supplier name in B2 (optional)
3. Enter date range in D2 and F2 (optional)
4. Run "RefreshAccountsData" macro to filter data

### Sample Data:
Import the sample data from `Sample_Data.csv` and `Sample_Bank_Statements.csv` to test your system.

Your invoice management system is now complete and ready to use!
